{"manifest_version": 3, "name": "Snap! Educational Assistant", "version": "0.1.0", "description": "Educational programming assistant that helps children create Snap! programs using natural language", "permissions": ["activeTab", "storage", "scripting", "notifications"], "host_permissions": ["https://snap.berkeley.edu/*", "https://extensions.snap.berkeley.edu/*", "http://localhost:8080/*"], "background": {"service_worker": "background.js"}, "content_scripts": [{"matches": ["https://snap.berkeley.edu/*", "https://extensions.snap.berkeley.edu/*"], "js": ["content_script.js"], "run_at": "document_end", "all_frames": false}], "web_accessible_resources": [{"resources": ["snap_bridge/bridge.js", "snap_bridge/block_creator.js", "snap_bridge/snap_api_wrapper.js", "snap_bridge/websocket_client.js", "snap_bridge/page_world_script.js", "snap_bridge/visual_feedback.js"], "matches": ["https://snap.berkeley.edu/*", "https://extensions.snap.berkeley.edu/*"]}], "action": {"default_popup": "popup.html", "default_title": "Snap! Educational Assistant", "default_icon": {"16": "icons/icon16.png", "32": "icons/icon32.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}}, "icons": {"16": "icons/icon16.png", "32": "icons/icon32.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}, "options_page": "options.html", "externally_connectable": {"matches": ["https://snap.berkeley.edu/*", "https://extensions.snap.berkeley.edu/*"]}, "content_security_policy": {"extension_pages": "script-src 'self'; object-src 'self'; connect-src 'self' ws://localhost:* wss://localhost:*;"}}